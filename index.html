<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Renamify - 智能文件重命名工具</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #5855eb;
            --secondary-color: #f3f4f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        [data-theme="dark"] {
            --primary-color: #818cf8;
            --primary-hover: #6366f1;
            --secondary-color: #374151;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --border-color: #4b5563;
            --bg-primary: #1f2937;
            --bg-secondary: #111827;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            width: 40px;
            height: 40px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            background: var(--secondary-color);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 24px;
            align-items: start;
        }

        .workspace {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
        }

        .sidebar {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
            position: sticky;
            top: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .drop-zone {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            margin-bottom: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .drop-zone:hover,
        .drop-zone.dragover {
            border-color: var(--primary-color);
            background: rgba(99, 102, 241, 0.05);
        }

        .drop-zone i {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .file-list {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 24px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: var(--secondary-color);
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: white;
            border-radius: 6px;
            font-size: 14px;
        }

        .file-names {
            flex: 1;
        }

        .original-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .new-name {
            font-size: 12px;
            color: var(--success-color);
            margin-top: 2px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .input-group {
            display: flex;
            gap: 8px;
        }

        .input-group .form-select {
            flex: 0 0 120px;
        }

        .input-group .form-input {
            flex: 1;
        }

        .preview-container {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            border: 1px solid var(--border-color);
        }

        .preview-title {
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
            font-size: 14px;
        }

        .preview-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: var(--bg-primary);
            border-radius: 6px;
            margin-bottom: 6px;
            font-size: 13px;
        }

        .preview-original {
            color: var(--text-secondary);
            flex: 1;
            margin-right: 12px;
            word-break: break-all;
        }

        .preview-arrow {
            color: var(--primary-color);
            margin: 0 8px;
        }

        .preview-new {
            color: var(--success-color);
            font-weight: 500;
            flex: 1;
            word-break: break-all;
        }

        .preset-list {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 16px;
        }

        .preset-item {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .preset-item:hover {
            background: var(--secondary-color);
        }

        .preset-item.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .preset-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .preset-pattern {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
        }

        .preset-item.active .preset-pattern {
            color: rgba(255, 255, 255, 0.8);
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }

            .header {
                padding: 16px;
            }

            .workspace,
            .sidebar {
                padding: 16px;
            }

            .controls {
                flex-wrap: wrap;
            }
        }

        .fade-in {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-magic"></i>
                Renamify
            </div>
            <div class="controls">
                <button class="btn btn-secondary" id="undoBtn">
                    <i class="fas fa-undo"></i>
                    撤销
                </button>
                <button class="btn btn-secondary" id="redoBtn">
                    <i class="fas fa-redo"></i>
                    重做
                </button>
                <button class="btn btn-primary" id="renameBtn">
                    <i class="fas fa-play"></i>
                    开始重命名
                </button>
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>

        <div class="main-content">
            <div class="workspace">
                <h2 class="section-title">文件工作区</h2>

                <div class="drop-zone" id="dropZone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <h3>拖拽文件到这里</h3>
                    <p>或点击选择文件</p>
                    <input type="file" id="fileInput" multiple style="display: none;">
                </div>

                <div class="file-list" id="fileList"></div>

                <div class="preview-container">
                    <div class="preview-title">
                        <i class="fas fa-eye"></i>
                        实时预览
                    </div>
                    <div id="previewArea">
                        <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                            添加文件后将显示重命名预览
                        </p>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <h2 class="section-title">重命名设置</h2>

                <div class="form-group">
                    <label class="form-label">前缀操作</label>
                    <div class="input-group">
                        <select class="form-select" id="prefixMode">
                            <option value="add">添加前缀</option>
                            <option value="replace">替换开头</option>
                        </select>
                        <input type="text" class="form-input" id="prefixInput" placeholder="例如：[2024]">
                    </div>
                    <input type="number" class="form-input" id="prefixLength" placeholder="替换字符数" style="margin-top: 8px; display: none;">
                </div>

                <div class="form-group">
                    <label class="form-label">后缀操作</label>
                    <div class="input-group">
                        <select class="form-select" id="suffixMode">
                            <option value="add">添加后缀</option>
                            <option value="replace">替换结尾</option>
                        </select>
                        <input type="text" class="form-input" id="suffixInput" placeholder="例如：_backup">
                    </div>
                    <input type="number" class="form-input" id="suffixLength" placeholder="替换字符数" style="margin-top: 8px; display: none;">
                </div>

                <div class="form-group">
                    <label class="form-label">查找模式 (正则表达式)</label>
                    <input type="text" class="form-input" id="findInput" placeholder="例如：\d{4}">
                </div>

                <div class="form-group">
                    <label class="form-label">替换为</label>
                    <input type="text" class="form-input" id="replaceInput" placeholder="例如：2024">
                </div>

                <h3 class="section-title" style="margin-top: 32px;">预设管理</h3>

                <div class="preset-list" id="presetList"></div>

                <div class="form-group">
                    <button class="btn btn-secondary" style="width: 100%;" id="addPresetBtn">
                        <i class="fas fa-plus"></i>
                        添加预设
                    </button>
                </div>

                <div class="form-group">
                    <button class="btn btn-secondary" style="width: 100%;" id="exportBtn">
                        <i class="fas fa-download"></i>
                        导出预设
                    </button>
                </div>

                <div class="form-group">
                    <button class="btn btn-secondary" style="width: 100%;" id="importBtn">
                        <i class="fas fa-upload"></i>
                        导入预设
                    </button>
                    <input type="file" id="importInput" accept=".json" style="display: none;">
                </div>
            </div>
        </div>
    </div>

    <script>
        // 应用状态管理
        class AppState {
            constructor() {
                this.files = [];
                this.history = [];
                this.historyIndex = -1;
                this.currentPreset = null;
                this.settings = {
                    prefix: '',
                    prefixMode: 'add',
                    prefixLength: 0,
                    suffix: '',
                    suffixMode: 'add',
                    suffixLength: 0,
                    findPattern: '',
                    replaceWith: ''
                };
                this.presets = this.loadPresets();
                this.theme = localStorage.getItem('theme') || 'light';
                this.initTheme();
            }

            initTheme() {
                document.documentElement.setAttribute('data-theme', this.theme);
                const themeIcon = document.querySelector('#themeToggle i');
                themeIcon.className = this.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            toggleTheme() {
                this.theme = this.theme === 'light' ? 'dark' : 'light';
                localStorage.setItem('theme', this.theme);
                this.initTheme();
            }

            loadPresets() {
                const saved = localStorage.getItem('renamify-presets');
                const defaultPresets = [
                    {
                        id: 'tv-show',
                        name: '电视剧格式',
                        pattern: '{name}.S{season:02d}E{episode:02d}.{title}.{quality}.{source}.{codec}',
                        description: '标准电视剧命名格式',
                        settings: {
                            prefix: '',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    },
                    {
                        id: 'movie',
                        name: '电影格式',
                        pattern: '{title}.{year}.{quality}.{source}.{codec}-{group}',
                        description: '标准电影命名格式',
                        settings: {
                            prefix: '',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    },
                    {
                        id: 'date-prefix',
                        name: '日期前缀',
                        pattern: '{date}_',
                        description: '添加当前日期作为前缀',
                        settings: {
                            prefix: new Date().toISOString().split('T')[0] + '_',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    }
                ];

                return saved ? [...defaultPresets, ...JSON.parse(saved)] : defaultPresets;
            }

            savePresets() {
                const customPresets = this.presets.filter(p => !['tv-show', 'movie', 'date-prefix'].includes(p.id));
                localStorage.setItem('renamify-presets', JSON.stringify(customPresets));
            }

            addToHistory(action) {
                this.history = this.history.slice(0, this.historyIndex + 1);
                this.history.push(action);
                this.historyIndex++;
                this.updateHistoryButtons();
            }

            undo() {
                if (this.historyIndex >= 0) {
                    const action = this.history[this.historyIndex];
                    this.executeAction(action, true);
                    this.historyIndex--;
                    this.updateHistoryButtons();
                }
            }

            redo() {
                if (this.historyIndex < this.history.length - 1) {
                    this.historyIndex++;
                    const action = this.history[this.historyIndex];
                    this.executeAction(action, false);
                    this.updateHistoryButtons();
                }
            }

            updateHistoryButtons() {
                document.getElementById('undoBtn').disabled = this.historyIndex < 0;
                document.getElementById('redoBtn').disabled = this.historyIndex >= this.history.length - 1;
            }

            executeAction(action, isUndo) {
                // 实现撤销/重做逻辑
                console.log('Execute action:', action, 'isUndo:', isUndo);
            }
        }

        // 初始化应用
        const app = new AppState();

        // DOM 元素引用
        const elements = {
            dropZone: document.getElementById('dropZone'),
            fileInput: document.getElementById('fileInput'),
            fileList: document.getElementById('fileList'),
            prefixInput: document.getElementById('prefixInput'),
            prefixMode: document.getElementById('prefixMode'),
            prefixLength: document.getElementById('prefixLength'),
            suffixInput: document.getElementById('suffixInput'),
            suffixMode: document.getElementById('suffixMode'),
            suffixLength: document.getElementById('suffixLength'),
            findInput: document.getElementById('findInput'),
            replaceInput: document.getElementById('replaceInput'),
            presetList: document.getElementById('presetList'),
            previewArea: document.getElementById('previewArea'),
            themeToggle: document.getElementById('themeToggle'),
            renameBtn: document.getElementById('renameBtn'),
            undoBtn: document.getElementById('undoBtn'),
            redoBtn: document.getElementById('redoBtn')
        };

        // 文件管理类
        class FileManager {
            constructor() {
                this.setupEventListeners();
            }

            setupEventListeners() {
                // 拖拽事件
                elements.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
                elements.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
                elements.dropZone.addEventListener('drop', this.handleDrop.bind(this));
                elements.dropZone.addEventListener('click', () => elements.fileInput.click());

                // 文件选择事件
                elements.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                elements.dropZone.classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                elements.dropZone.classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                elements.dropZone.classList.remove('dragover');

                const files = Array.from(e.dataTransfer.files);
                this.addFiles(files);
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                this.addFiles(files);
            }

            addFiles(files) {
                files.forEach(file => {
                    if (!app.files.find(f => f.name === file.name && f.size === file.size)) {
                        const fileObj = {
                            id: Date.now() + Math.random(),
                            name: file.name,
                            originalName: file.name,
                            size: file.size,
                            type: file.type,
                            file: file
                        };
                        app.files.push(fileObj);
                    }
                });

                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();
            }

            removeFile(fileId) {
                app.files = app.files.filter(f => f.id !== fileId);
                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();
            }

            renderFileList() {
                if (app.files.length === 0) {
                    elements.fileList.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 20px;">暂无文件</p>';
                    return;
                }

                elements.fileList.innerHTML = app.files.map(file => `
                    <div class="file-item fade-in">
                        <div class="file-info">
                            <div class="file-icon">
                                <i class="fas ${this.getFileIcon(file.type)}"></i>
                            </div>
                            <div class="file-names">
                                <div class="original-name">${file.originalName}</div>
                                <div class="new-name" id="preview-${file.id}"></div>
                            </div>
                        </div>
                        <button class="btn btn-secondary" onclick="fileManager.removeFile('${file.id}')" style="padding: 8px;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('');
            }

            getFileIcon(type) {
                if (type.startsWith('image/')) return 'fa-image';
                if (type.startsWith('video/')) return 'fa-video';
                if (type.startsWith('audio/')) return 'fa-music';
                if (type.includes('pdf')) return 'fa-file-pdf';
                if (type.includes('word')) return 'fa-file-word';
                if (type.includes('excel')) return 'fa-file-excel';
                if (type.includes('powerpoint')) return 'fa-file-powerpoint';
                if (type.includes('zip') || type.includes('rar')) return 'fa-file-archive';
                return 'fa-file';
            }
        }

        // 重命名引擎类
        class RenameEngine {
            constructor() {
                this.setupEventListeners();
            }

            setupEventListeners() {
                // 输入框事件
                elements.prefixInput.addEventListener('input', this.updateSettings.bind(this));
                elements.prefixMode.addEventListener('change', this.handlePrefixModeChange.bind(this));
                elements.prefixLength.addEventListener('input', this.updateSettings.bind(this));
                elements.suffixInput.addEventListener('input', this.updateSettings.bind(this));
                elements.suffixMode.addEventListener('change', this.handleSuffixModeChange.bind(this));
                elements.suffixLength.addEventListener('input', this.updateSettings.bind(this));
                elements.findInput.addEventListener('input', this.updateSettings.bind(this));
                elements.replaceInput.addEventListener('input', this.updateSettings.bind(this));

                // 重命名按钮
                elements.renameBtn.addEventListener('click', this.executeRename.bind(this));
            }

            handlePrefixModeChange() {
                const isReplace = elements.prefixMode.value === 'replace';
                elements.prefixLength.style.display = isReplace ? 'block' : 'none';
                elements.prefixLength.placeholder = isReplace ? '要替换的字符数' : '';
                this.updateSettings();
            }

            handleSuffixModeChange() {
                const isReplace = elements.suffixMode.value === 'replace';
                elements.suffixLength.style.display = isReplace ? 'block' : 'none';
                elements.suffixLength.placeholder = isReplace ? '要替换的字符数' : '';
                this.updateSettings();
            }

            updateSettings() {
                app.settings = {
                    prefix: elements.prefixInput.value,
                    prefixMode: elements.prefixMode.value,
                    prefixLength: parseInt(elements.prefixLength.value) || 0,
                    suffix: elements.suffixInput.value,
                    suffixMode: elements.suffixMode.value,
                    suffixLength: parseInt(elements.suffixLength.value) || 0,
                    findPattern: elements.findInput.value,
                    replaceWith: elements.replaceInput.value
                };
                this.updatePreview();
                this.updatePreviewArea();
            }

            updatePreview() {
                app.files.forEach(file => {
                    const newName = this.generateNewName(file.originalName);
                    file.newName = newName;

                    const previewElement = document.getElementById(`preview-${file.id}`);
                    if (previewElement) {
                        previewElement.textContent = newName !== file.originalName ? `→ ${newName}` : '';
                    }
                });
            }

            generateNewName(originalName) {
                let newName = originalName;
                const {
                    prefix, prefixMode, prefixLength,
                    suffix, suffixMode, suffixLength,
                    findPattern, replaceWith
                } = app.settings;

                // 应用正则表达式替换
                if (findPattern) {
                    try {
                        const regex = new RegExp(findPattern, 'g');
                        newName = newName.replace(regex, replaceWith);
                    } catch (e) {
                        console.warn('Invalid regex pattern:', findPattern);
                    }
                }

                // 分离文件名和扩展名
                const lastDotIndex = newName.lastIndexOf('.');
                let nameWithoutExt = newName;
                let extension = '';

                if (lastDotIndex > 0) {
                    nameWithoutExt = newName.substring(0, lastDotIndex);
                    extension = newName.substring(lastDotIndex);
                }

                // 处理前缀
                if (prefix) {
                    if (prefixMode === 'replace' && prefixLength > 0) {
                        // 替换开头指定长度的字符
                        nameWithoutExt = prefix + nameWithoutExt.substring(Math.min(prefixLength, nameWithoutExt.length));
                    } else {
                        // 添加前缀
                        nameWithoutExt = prefix + nameWithoutExt;
                    }
                }

                // 处理后缀
                if (suffix) {
                    if (suffixMode === 'replace' && suffixLength > 0) {
                        // 替换结尾指定长度的字符
                        const replaceStart = Math.max(0, nameWithoutExt.length - suffixLength);
                        nameWithoutExt = nameWithoutExt.substring(0, replaceStart) + suffix;
                    } else {
                        // 添加后缀
                        nameWithoutExt = nameWithoutExt + suffix;
                    }
                }

                return nameWithoutExt + extension;
            }

            updatePreviewArea() {
                if (app.files.length === 0) {
                    elements.previewArea.innerHTML = `
                        <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                            添加文件后将显示重命名预览
                        </p>
                    `;
                    return;
                }

                const previewItems = app.files.slice(0, 5).map(file => {
                    const newName = this.generateNewName(file.originalName);
                    const hasChanges = newName !== file.originalName;

                    return `
                        <div class="preview-item">
                            <div class="preview-original">${file.originalName}</div>
                            ${hasChanges ? `
                                <div class="preview-arrow">→</div>
                                <div class="preview-new">${newName}</div>
                            ` : `
                                <div style="color: var(--text-secondary); font-style: italic;">无变化</div>
                            `}
                        </div>
                    `;
                }).join('');

                const moreFiles = app.files.length > 5 ? `
                    <div style="text-align: center; color: var(--text-secondary); font-size: 12px; margin-top: 8px;">
                        还有 ${app.files.length - 5} 个文件...
                    </div>
                ` : '';

                elements.previewArea.innerHTML = previewItems + moreFiles;
            }

            async executeRename() {
                if (app.files.length === 0) {
                    this.showNotification('请先添加文件', 'warning');
                    return;
                }

                const button = elements.renameBtn;
                const originalText = button.innerHTML;
                button.innerHTML = '<div class="loading"></div> 重命名中...';
                button.disabled = true;

                try {
                    // 模拟重命名过程（实际应用中这里会调用文件系统API）
                    await this.simulateRename();

                    // 添加到历史记录
                    app.addToHistory({
                        type: 'rename',
                        files: app.files.map(f => ({
                            id: f.id,
                            oldName: f.originalName,
                            newName: f.newName
                        })),
                        settings: { ...app.settings }
                    });

                    this.showNotification('重命名完成！', 'success');

                    // 更新原始名称为新名称
                    app.files.forEach(file => {
                        file.originalName = file.newName;
                    });

                    fileManager.renderFileList();
                    this.updatePreview();

                } catch (error) {
                    this.showNotification('重命名失败：' + error.message, 'error');
                } finally {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }

            async simulateRename() {
                // 模拟异步重命名过程
                return new Promise(resolve => {
                    setTimeout(resolve, 1000);
                });
            }

            showNotification(message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 16px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                    max-width: 300px;
                `;

                // 设置背景色
                const colors = {
                    success: '#10b981',
                    warning: '#f59e0b',
                    error: '#ef4444',
                    info: '#6366f1'
                };
                notification.style.background = colors[type];

                notification.textContent = message;
                document.body.appendChild(notification);

                // 自动移除
                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        }

        // 预设管理类
        class PresetManager {
            constructor() {
                this.setupEventListeners();
                this.renderPresets();
            }

            setupEventListeners() {
                document.getElementById('addPresetBtn').addEventListener('click', this.showAddPresetDialog.bind(this));
                document.getElementById('exportBtn').addEventListener('click', this.exportPresets.bind(this));
                document.getElementById('importBtn').addEventListener('click', () => {
                    document.getElementById('importInput').click();
                });
                document.getElementById('importInput').addEventListener('change', this.importPresets.bind(this));
            }

            renderPresets() {
                elements.presetList.innerHTML = app.presets.map(preset => `
                    <div class="preset-item ${app.currentPreset?.id === preset.id ? 'active' : ''}"
                         onclick="presetManager.selectPreset('${preset.id}')">
                        <div class="preset-name">${preset.name}</div>
                        <div class="preset-pattern">${preset.pattern || preset.description}</div>
                        ${!['tv-show', 'movie', 'date-prefix'].includes(preset.id) ?
                            `<button onclick="event.stopPropagation(); presetManager.deletePreset('${preset.id}')"
                                     style="float: right; background: none; border: none; color: var(--text-secondary); cursor: pointer;">
                                <i class="fas fa-times"></i>
                             </button>` : ''
                        }
                    </div>
                `).join('');
            }

            selectPreset(presetId) {
                const preset = app.presets.find(p => p.id === presetId);
                if (!preset) return;

                app.currentPreset = preset;

                // 应用预设设置
                if (preset.settings) {
                    elements.prefixInput.value = preset.settings.prefix || '';
                    elements.suffixInput.value = preset.settings.suffix || '';
                    elements.findInput.value = preset.settings.findPattern || '';
                    elements.replaceInput.value = preset.settings.replaceWith || '';

                    renameEngine.updateSettings();
                }

                this.renderPresets();
            }

            showAddPresetDialog() {
                const name = prompt('请输入预设名称：');
                if (!name) return;

                const description = prompt('请输入预设描述（可选）：') || '';

                const preset = {
                    id: Date.now().toString(),
                    name: name,
                    description: description,
                    pattern: `${app.settings.prefix}{name}${app.settings.suffix}`,
                    settings: { ...app.settings }
                };

                app.presets.push(preset);
                app.savePresets();
                this.renderPresets();

                renameEngine.showNotification('预设添加成功！', 'success');
            }

            deletePreset(presetId) {
                if (confirm('确定要删除这个预设吗？')) {
                    app.presets = app.presets.filter(p => p.id !== presetId);
                    if (app.currentPreset?.id === presetId) {
                        app.currentPreset = null;
                    }
                    app.savePresets();
                    this.renderPresets();
                    renameEngine.showNotification('预设删除成功！', 'success');
                }
            }

            exportPresets() {
                const customPresets = app.presets.filter(p => !['tv-show', 'movie', 'date-prefix'].includes(p.id));
                const dataStr = JSON.stringify(customPresets, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = 'renamify-presets.json';
                link.click();

                renameEngine.showNotification('预设导出成功！', 'success');
            }

            importPresets(e) {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (event) => {
                    try {
                        const presets = JSON.parse(event.target.result);
                        if (Array.isArray(presets)) {
                            // 避免ID冲突
                            presets.forEach(preset => {
                                preset.id = Date.now() + Math.random();
                            });

                            app.presets.push(...presets);
                            app.savePresets();
                            this.renderPresets();
                            renameEngine.showNotification(`成功导入 ${presets.length} 个预设！`, 'success');
                        } else {
                            throw new Error('无效的预设文件格式');
                        }
                    } catch (error) {
                        renameEngine.showNotification('导入失败：' + error.message, 'error');
                    }
                };
                reader.readAsText(file);

                // 清空input
                e.target.value = '';
            }
        }

        // 初始化所有管理器
        const fileManager = new FileManager();
        const renameEngine = new RenameEngine();
        const presetManager = new PresetManager();

        // 主题切换
        elements.themeToggle.addEventListener('click', () => {
            app.toggleTheme();
        });

        // 历史操作
        elements.undoBtn.addEventListener('click', () => app.undo());
        elements.redoBtn.addEventListener('click', () => app.redo());

        // 快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'z':
                        e.preventDefault();
                        if (e.shiftKey) {
                            app.redo();
                        } else {
                            app.undo();
                        }
                        break;
                    case 'Enter':
                        e.preventDefault();
                        renameEngine.executeRename();
                        break;
                }
            }
        });

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            .notification {
                box-shadow: var(--shadow-lg);
            }
        `;
        document.head.appendChild(style);

        // 初始化历史按钮状态
        app.updateHistoryButtons();

        console.log('Renamify 应用初始化完成！');
    </script>
</body>
</html>
