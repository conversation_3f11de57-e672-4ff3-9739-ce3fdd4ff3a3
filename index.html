<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Renamify - 智能文件重命名工具</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #5855eb;
            --secondary-color: #f3f4f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        [data-theme="dark"] {
            --primary-color: #818cf8;
            --primary-hover: #6366f1;
            --secondary-color: #374151;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --border-color: #4b5563;
            --bg-primary: #1f2937;
            --bg-secondary: #111827;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            width: 40px;
            height: 40px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            background: var(--secondary-color);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 24px;
            align-items: start;
        }

        .workspace {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
        }

        .sidebar {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
            position: sticky;
            top: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .drop-zone {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            margin-bottom: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .drop-zone:hover,
        .drop-zone.dragover {
            border-color: var(--primary-color);
            background: rgba(99, 102, 241, 0.05);
        }

        .drop-zone i {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .file-list {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 24px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: var(--secondary-color);
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: white;
            border-radius: 6px;
            font-size: 14px;
        }

        .file-names {
            flex: 1;
        }

        .original-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .new-name {
            font-size: 12px;
            color: var(--success-color);
            margin-top: 2px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .input-group {
            display: flex;
            gap: 8px;
        }

        .input-group .form-select {
            flex: 0 0 120px;
        }

        .input-group .form-input {
            flex: 1;
        }

        .preview-container {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            border: 1px solid var(--border-color);
        }

        .preview-title {
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
            font-size: 14px;
        }

        .preview-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: var(--bg-primary);
            border-radius: 6px;
            margin-bottom: 6px;
            font-size: 13px;
        }

        .preview-original {
            color: var(--text-secondary);
            flex: 1;
            margin-right: 12px;
            word-break: break-all;
        }

        .preview-arrow {
            color: var(--primary-color);
            margin: 0 8px;
        }

        .preview-new {
            color: var(--success-color);
            font-weight: 500;
            flex: 1;
            word-break: break-all;
        }

        .form-hint {
            display: block;
            margin-top: 4px;
            font-size: 12px;
            color: var(--text-secondary);
            font-style: italic;
        }

        .regex-controls {
            margin-bottom: 12px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: var(--text-primary);
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            position: relative;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .regex-help {
            background: var(--secondary-color);
            border-radius: 6px;
            padding: 12px;
            margin-top: 8px;
            font-size: 12px;
            line-height: 1.4;
            display: none;
        }

        .regex-help.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        .regex-example {
            font-family: 'Courier New', monospace;
            background: var(--bg-primary);
            padding: 2px 4px;
            border-radius: 3px;
            margin: 0 2px;
        }

        /* 格式选择器样式 */
        .format-selector {
            margin-bottom: 20px;
        }

        .format-tabs {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .format-tab {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
        }

        .format-tab:hover {
            background: var(--secondary-color);
        }

        .format-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 模板显示样式 */
        .format-template {
            margin-bottom: 20px;
        }

        .template-preview {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: var(--primary-color);
            word-break: break-all;
            margin-top: 8px;
        }

        /* 占位符输入样式 */
        .placeholder-inputs {
            margin-bottom: 20px;
        }

        .placeholder-group {
            margin-bottom: 12px;
        }

        .placeholder-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .placeholder-code {
            font-family: 'Courier New', monospace;
            color: var(--primary-color);
            font-size: 11px;
        }

        .placeholder-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .placeholder-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
        }

        /* 格式预览样式 */
        .format-preview {
            margin-bottom: 20px;
        }

        .preview-result {
            background: var(--success-color);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            word-break: break-all;
            margin-top: 8px;
            min-height: 20px;
        }

        .preview-result.empty {
            background: var(--secondary-color);
            color: var(--text-secondary);
            font-family: 'Inter', sans-serif;
            font-style: italic;
        }

        /* 格式操作按钮样式 */
        .format-actions {
            margin-bottom: 20px;
        }

        /* 已保存模板样式 */
        .saved-templates {
            margin-bottom: 20px;
        }

        .template-list {
            max-height: 150px;
            overflow-y: auto;
        }

        .template-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--bg-primary);
        }

        .template-item:hover {
            background: var(--secondary-color);
        }

        .template-item.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .template-info {
            flex: 1;
        }

        .template-name {
            font-weight: 500;
            font-size: 13px;
            margin-bottom: 2px;
        }

        .template-format {
            font-size: 11px;
            font-family: 'Courier New', monospace;
            opacity: 0.8;
        }

        .template-actions {
            display: flex;
            gap: 4px;
        }

        .template-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .template-action:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .template-item.active .template-action {
            color: rgba(255, 255, 255, 0.8);
        }

        .template-item.active .template-action:hover {
            color: white;
        }

        /* 模板管理样式 */
        .template-management {
            display: flex;
            gap: 8px;
        }

        /* 文件管理样式 */
        .file-management {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .file-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .file-stats {
            font-size: 14px;
            color: var(--text-secondary);
            padding: 8px 0;
            border-top: 1px solid var(--border-color);
        }

        .file-path {
            font-size: 11px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
            margin-top: 2px;
            word-break: break-all;
        }

        .file-item {
            position: relative;
        }

        .file-item.selected {
            background: rgba(99, 102, 241, 0.1);
            border-color: var(--primary-color);
        }

        .file-item .file-checkbox {
            position: absolute;
            top: 12px;
            right: 40px;
            width: 16px;
            height: 16px;
        }

        .batch-actions {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            display: none;
        }

        .batch-actions.show {
            display: block;
        }

        .batch-info {
            font-size: 14px;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .auto-episode {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
        }

        .auto-episode-label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .episode-settings {
            display: none;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }

        .episode-settings.show {
            display: flex;
        }

        .episode-input {
            width: 80px;
            padding: 6px 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 13px;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 8px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .notification.warning {
            border-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .notification.error {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .notification.info {
            border-color: var(--primary-color);
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }



        .preset-list {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 16px;
        }

        .preset-item {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .preset-item:hover {
            background: var(--secondary-color);
        }

        .preset-item.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .preset-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .preset-pattern {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
        }

        .preset-item.active .preset-pattern {
            color: rgba(255, 255, 255, 0.8);
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }

            .header {
                padding: 16px;
            }

            .workspace,
            .sidebar {
                padding: 16px;
            }

            .controls {
                flex-wrap: wrap;
            }
        }

        .fade-in {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-magic"></i>
                Renamify
            </div>
            <div class="controls">
                <button class="btn btn-secondary" id="undoBtn">
                    <i class="fas fa-undo"></i>
                    撤销
                </button>
                <button class="btn btn-secondary" id="redoBtn">
                    <i class="fas fa-redo"></i>
                    重做
                </button>
                <button class="btn btn-primary" id="renameBtn">
                    <i class="fas fa-play"></i>
                    开始重命名
                </button>
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>

        <div class="main-content">
            <div class="workspace">
                <h2 class="section-title">文件工作区</h2>

                <div class="file-management">
                    <div class="file-actions">
                        <button class="btn btn-primary" id="addFilesBtn">
                            <i class="fas fa-plus"></i>
                            添加文件
                        </button>
                        <button class="btn btn-secondary" id="addFolderBtn">
                            <i class="fas fa-folder-plus"></i>
                            添加文件夹
                        </button>
                        <button class="btn btn-secondary" id="clearAllBtn">
                            <i class="fas fa-trash"></i>
                            清空列表
                        </button>
                    </div>
                    <div class="file-stats" id="fileStats">
                        <span>已选择 0 个文件</span>
                    </div>
                </div>

                <div class="drop-zone" id="dropZone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <h3>拖拽文件到这里</h3>
                    <p>或点击上方按钮选择文件/文件夹</p>
                    <input type="file" id="fileInput" multiple style="display: none;">
                    <input type="file" id="folderInput" webkitdirectory multiple style="display: none;">
                </div>

                <div class="batch-actions" id="batchActions">
                    <div class="batch-info">
                        <span id="selectedCount">已选择 0 个文件</span>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-primary" id="batchRenameBtn">
                            <i class="fas fa-edit"></i>
                            批量重命名
                        </button>
                        <button class="btn btn-secondary" id="selectAllBtn">
                            <i class="fas fa-check-square"></i>
                            全选
                        </button>
                        <button class="btn btn-secondary" id="deselectAllBtn">
                            <i class="fas fa-square"></i>
                            取消全选
                        </button>
                        <button class="btn btn-danger" id="removeSelectedBtn">
                            <i class="fas fa-trash"></i>
                            移除选中
                        </button>
                    </div>
                </div>

                <div class="file-list" id="fileList"></div>

                <div class="preview-container">
                    <div class="preview-title">
                        <i class="fas fa-eye"></i>
                        实时预览
                    </div>
                    <div id="previewArea">
                        <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                            添加文件后将显示重命名预览
                        </p>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <h2 class="section-title">重命名设置</h2>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-plus-circle"></i>
                        前缀操作
                    </label>
                    <input type="text" class="form-input" id="prefixInput" placeholder="例如：[2024] 或 New_">
                    <small class="form-hint">在文件名开头添加指定内容（可选）</small>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-plus-circle"></i>
                        后缀操作
                    </label>
                    <input type="text" class="form-input" id="suffixInput" placeholder="例如：_backup 或 _new">
                    <small class="form-hint">在文件名结尾（扩展名前）添加指定内容（可选）</small>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-search"></i>
                        查找替换操作
                    </label>
                    <div class="regex-controls">
                        <label class="checkbox-label">
                            <input type="checkbox" id="regexMode">
                            <span class="checkmark"></span>
                            启用正则表达式模式
                        </label>
                    </div>
                    <input type="text" class="form-input" id="findInput" placeholder="查找内容（例如：old 或 \d{4}）">
                    <input type="text" class="form-input" id="replaceInput" placeholder="替换为（例如：new 或 2024）" style="margin-top: 8px;">
                    <small class="form-hint" id="regexHint">普通文本查找替换模式</small>
                    <div class="regex-help" id="regexHelp">
                        <strong>常用正则表达式示例：</strong><br>
                        <span class="regex-example">\d+</span> 匹配数字 |
                        <span class="regex-example">.*</span> 匹配任意字符 |
                        <span class="regex-example">[a-z]+</span> 匹配小写字母<br>
                        <span class="regex-example">^</span> 开头 |
                        <span class="regex-example">$</span> 结尾 |
                        <span class="regex-example">\s+</span> 匹配空格
                    </div>
                </div>

                <div class="auto-episode" id="autoEpisode">
                    <h3 class="section-title">
                        <i class="fas fa-list-ol"></i>
                        自动集数递增
                    </h3>
                    <label class="auto-episode-label">
                        <input type="checkbox" id="autoEpisodeCheck">
                        <span>启用自动集数递增</span>
                        <i class="fas fa-info-circle" title="为电视剧自动添加递增的集数编号"></i>
                    </label>
                    <div class="episode-settings" id="episodeSettings">
                        <label>起始集数：
                            <input type="number" id="startEpisode" class="episode-input" value="1" min="1">
                        </label>
                        <label>集数位数：
                            <input type="number" id="episodeDigits" class="episode-input" value="2" min="1" max="4">
                        </label>
                        <label>集数前缀：
                            <input type="text" id="episodePrefix" class="episode-input" value="E" maxlength="5">
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 应用状态管理
        class AppState {
            constructor() {
                this.files = [];
                this.history = [];
                this.historyIndex = -1;
                this.currentPreset = null;
                this.settings = {
                    prefix: '',
                    suffix: '',
                    findPattern: '',
                    replaceWith: '',
                    isRegexMode: false
                };
                this.presets = this.loadPresets();
                this.theme = localStorage.getItem('theme') || 'light';
                this.initTheme();
            }

            initTheme() {
                document.documentElement.setAttribute('data-theme', this.theme);
                const themeIcon = document.querySelector('#themeToggle i');
                themeIcon.className = this.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            toggleTheme() {
                this.theme = this.theme === 'light' ? 'dark' : 'light';
                localStorage.setItem('theme', this.theme);
                this.initTheme();
            }

            loadPresets() {
                const saved = localStorage.getItem('renamify-presets');
                const defaultPresets = [
                    {
                        id: 'tv-show',
                        name: '电视剧格式',
                        pattern: '{name}.S{season:02d}E{episode:02d}.{title}.{quality}.{source}.{codec}',
                        description: '标准电视剧命名格式',
                        settings: {
                            prefix: '',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    },
                    {
                        id: 'movie',
                        name: '电影格式',
                        pattern: '{title}.{year}.{quality}.{source}.{codec}-{group}',
                        description: '标准电影命名格式',
                        settings: {
                            prefix: '',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    },
                    {
                        id: 'date-prefix',
                        name: '日期前缀',
                        pattern: '{date}_',
                        description: '添加当前日期作为前缀',
                        settings: {
                            prefix: new Date().toISOString().split('T')[0] + '_',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    }
                ];

                return saved ? [...defaultPresets, ...JSON.parse(saved)] : defaultPresets;
            }

            savePresets() {
                const customPresets = this.presets.filter(p => !['tv-show', 'movie', 'date-prefix'].includes(p.id));
                localStorage.setItem('renamify-presets', JSON.stringify(customPresets));
            }

            addToHistory(action) {
                this.history = this.history.slice(0, this.historyIndex + 1);
                this.history.push(action);
                this.historyIndex++;
                this.updateHistoryButtons();
            }

            undo() {
                if (this.historyIndex >= 0) {
                    const action = this.history[this.historyIndex];
                    this.executeAction(action, true);
                    this.historyIndex--;
                    this.updateHistoryButtons();
                }
            }

            redo() {
                if (this.historyIndex < this.history.length - 1) {
                    this.historyIndex++;
                    const action = this.history[this.historyIndex];
                    this.executeAction(action, false);
                    this.updateHistoryButtons();
                }
            }

            updateHistoryButtons() {
                document.getElementById('undoBtn').disabled = this.historyIndex < 0;
                document.getElementById('redoBtn').disabled = this.historyIndex >= this.history.length - 1;
            }

            executeAction(action, isUndo) {
                // 实现撤销/重做逻辑
                console.log('Execute action:', action, 'isUndo:', isUndo);
            }
        }



        // 初始化应用
        const app = new AppState();

        // DOM 元素引用
        const elements = {
            dropZone: document.getElementById('dropZone'),
            fileInput: document.getElementById('fileInput'),
            fileList: document.getElementById('fileList'),
            prefixInput: document.getElementById('prefixInput'),
            suffixInput: document.getElementById('suffixInput'),
            findInput: document.getElementById('findInput'),
            replaceInput: document.getElementById('replaceInput'),
            regexMode: document.getElementById('regexMode'),
            regexHint: document.getElementById('regexHint'),
            regexHelp: document.getElementById('regexHelp'),
            previewArea: document.getElementById('previewArea'),
            themeToggle: document.getElementById('themeToggle'),
            renameBtn: document.getElementById('renameBtn'),
            undoBtn: document.getElementById('undoBtn'),
            redoBtn: document.getElementById('redoBtn')
        };

        // 文件管理类
        class FileManager {
            constructor() {
                this.setupEventListeners();
            }

            setupEventListeners() {
                // 拖拽事件
                elements.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
                elements.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
                elements.dropZone.addEventListener('drop', this.handleDrop.bind(this));
                elements.dropZone.addEventListener('click', () => elements.fileInput.click());

                // 文件选择事件
                elements.fileInput.addEventListener('change', this.handleFileSelect.bind(this));

                // 文件夹选择事件
                const folderInput = document.getElementById('folderInput');
                folderInput.addEventListener('change', this.handleFileSelect.bind(this));

                // 文件管理按钮事件
                document.getElementById('addFilesBtn').addEventListener('click', () => {
                    elements.fileInput.click();
                });

                document.getElementById('addFolderBtn').addEventListener('click', () => {
                    folderInput.click();
                });

                document.getElementById('clearAllBtn').addEventListener('click', () => {
                    this.clearAllFiles();
                });

                document.getElementById('selectAllBtn').addEventListener('click', () => {
                    this.selectAllFiles();
                });

                document.getElementById('deselectAllBtn').addEventListener('click', () => {
                    this.deselectAllFiles();
                });

                document.getElementById('removeSelectedBtn').addEventListener('click', () => {
                    this.removeSelectedFiles();
                });

                document.getElementById('batchRenameBtn').addEventListener('click', () => {
                    this.batchRename();
                });
            }

            handleDragOver(e) {
                e.preventDefault();
                elements.dropZone.classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                elements.dropZone.classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                elements.dropZone.classList.remove('dragover');

                const files = Array.from(e.dataTransfer.files);
                this.addFiles(files);
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                this.addFiles(files);
            }

            addFiles(files) {
                files.forEach(file => {
                    // 创建唯一标识符，包含路径信息
                    const filePath = file.webkitRelativePath || file.name;
                    const uniqueId = filePath + '_' + file.size + '_' + file.lastModified;

                    if (!app.files.find(f => f.uniqueId === uniqueId)) {
                        const fileObj = {
                            id: Date.now() + Math.random(),
                            uniqueId: uniqueId,
                            name: file.name,
                            originalName: file.name,
                            path: file.webkitRelativePath ? file.webkitRelativePath.replace(file.name, '').replace(/\/$/, '') : '',
                            size: file.size,
                            type: file.type,
                            file: file,
                            selected: false
                        };
                        app.files.push(fileObj);
                    }
                });

                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();
            }

            removeFile(fileId) {
                app.files = app.files.filter(f => f.id !== fileId);
                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();
            }

            renderFileList() {
                if (app.files.length === 0) {
                    elements.fileList.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 20px;">暂无文件</p>';
                    this.updateFileStats();
                    return;
                }

                elements.fileList.innerHTML = app.files.map(file => `
                    <div class="file-item fade-in ${file.selected ? 'selected' : ''}" data-file-id="${file.id}">
                        <div class="file-info">
                            <div class="file-icon">
                                <i class="fas ${this.getFileIcon(file.type)}"></i>
                            </div>
                            <div class="file-names">
                                <div class="original-name">${file.originalName}</div>
                                <div class="file-path">${file.path || ''}</div>
                                <div class="new-name" id="preview-${file.id}"></div>
                            </div>
                        </div>
                        <input type="checkbox" class="file-checkbox" ${file.selected ? 'checked' : ''}
                               onchange="fileManager.toggleFileSelection('${file.id}', this.checked)">
                        <button class="btn btn-secondary" onclick="fileManager.removeFile('${file.id}')" style="padding: 8px;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('');

                this.updateFileStats();
            }

            updateFileStats() {
                const total = app.files.length;
                const selected = app.files.filter(f => f.selected).length;

                document.getElementById('fileStats').innerHTML = `
                    <span>已选择 ${total} 个文件</span>
                `;

                document.getElementById('selectedCount').textContent = `已选择 ${selected} 个文件`;

                const batchActions = document.getElementById('batchActions');
                if (selected > 0) {
                    batchActions.classList.add('show');
                } else {
                    batchActions.classList.remove('show');
                }
            }

            toggleFileSelection(fileId, selected) {
                const file = app.files.find(f => f.id === fileId);
                if (file) {
                    file.selected = selected;
                    this.renderFileList();
                }
            }

            selectAllFiles() {
                app.files.forEach(file => file.selected = true);
                this.renderFileList();
            }

            deselectAllFiles() {
                app.files.forEach(file => file.selected = false);
                this.renderFileList();
            }

            removeSelectedFiles() {
                app.files = app.files.filter(f => !f.selected);
                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();
            }

            clearAllFiles() {
                app.files = [];
                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();
            }

            async batchRename() {
                const selectedFiles = app.files.filter(f => f.selected);
                if (selectedFiles.length === 0) {
                    this.showNotification('请先选择要重命名的文件', 'warning');
                    return;
                }

                const button = document.getElementById('batchRenameBtn');
                const originalText = button.innerHTML;
                button.innerHTML = '<div class="loading"></div> 重命名中...';
                button.disabled = true;

                try {
                    // 模拟批量重命名过程
                    await this.simulateBatchRename(selectedFiles);

                    this.showNotification(`成功重命名 ${selectedFiles.length} 个文件`, 'success');

                    // 更新文件名
                    selectedFiles.forEach(file => {
                        file.originalName = file.newName || file.originalName;
                        file.selected = false;
                    });

                    this.renderFileList();
                    renameEngine.updatePreview();
                    renameEngine.updatePreviewArea();

                } catch (error) {
                    this.showNotification('重命名失败: ' + error.message, 'error');
                } finally {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }

            async simulateBatchRename(files) {
                // 模拟重命名延迟
                for (let i = 0; i < files.length; i++) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    // 这里在实际应用中会调用文件系统API进行真实的重命名
                    console.log(`Renaming: ${files[i].originalName} → ${files[i].newName || files[i].originalName}`);
                }
            }

            showNotification(message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="fas ${type === 'success' ? 'fa-check-circle' :
                                   type === 'warning' ? 'fa-exclamation-triangle' :
                                   type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
                    <span>${message}</span>
                `;

                // 添加到页面
                document.body.appendChild(notification);

                // 显示动画
                setTimeout(() => notification.classList.add('show'), 100);

                // 自动移除
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);
            }

            getFileIcon(type) {
                if (type.startsWith('image/')) return 'fa-image';
                if (type.startsWith('video/')) return 'fa-video';
                if (type.startsWith('audio/')) return 'fa-music';
                if (type.includes('pdf')) return 'fa-file-pdf';
                if (type.includes('word')) return 'fa-file-word';
                if (type.includes('excel')) return 'fa-file-excel';
                if (type.includes('powerpoint')) return 'fa-file-powerpoint';
                if (type.includes('zip') || type.includes('rar')) return 'fa-file-archive';
                return 'fa-file';
            }
        }

        // 重命名引擎类
        class RenameEngine {
            constructor() {
                this.setupEventListeners();
            }

            setupEventListeners() {
                // 输入框事件
                elements.prefixInput.addEventListener('input', this.updateSettings.bind(this));
                elements.suffixInput.addEventListener('input', this.updateSettings.bind(this));
                elements.findInput.addEventListener('input', this.updateSettings.bind(this));
                elements.replaceInput.addEventListener('input', this.updateSettings.bind(this));
                elements.regexMode.addEventListener('change', this.handleRegexModeChange.bind(this));

                // 重命名按钮
                elements.renameBtn.addEventListener('click', this.executeRename.bind(this));

                // 自动集数功能事件
                const autoEpisodeCheck = document.getElementById('autoEpisodeCheck');
                const episodeSettings = document.getElementById('episodeSettings');
                const startEpisode = document.getElementById('startEpisode');
                const episodeDigits = document.getElementById('episodeDigits');
                const episodePrefix = document.getElementById('episodePrefix');

                if (autoEpisodeCheck) {
                    autoEpisodeCheck.addEventListener('change', (e) => {
                        if (e.target.checked) {
                            episodeSettings.classList.add('show');
                        } else {
                            episodeSettings.classList.remove('show');
                        }
                        this.updatePreview();
                    });
                }

                [startEpisode, episodeDigits, episodePrefix].forEach(element => {
                    if (element) {
                        element.addEventListener('input', this.updatePreview.bind(this));
                    }
                });
            }

            handleRegexModeChange() {
                const isRegexMode = elements.regexMode.checked;

                if (isRegexMode) {
                    elements.regexHint.textContent = '正则表达式模式已启用';
                    elements.regexHelp.classList.add('show');
                    elements.findInput.placeholder = '正则表达式（例如：\\d{4} 或 old.*）';
                    elements.replaceInput.placeholder = '替换内容（例如：2024 或 new）';
                } else {
                    elements.regexHint.textContent = '普通文本查找替换模式';
                    elements.regexHelp.classList.remove('show');
                    elements.findInput.placeholder = '查找内容（例如：old）';
                    elements.replaceInput.placeholder = '替换为（例如：new）';
                }

                this.updateSettings();
            }

            updateSettings() {
                app.settings = {
                    prefix: elements.prefixInput.value,
                    suffix: elements.suffixInput.value,
                    findPattern: elements.findInput.value,
                    replaceWith: elements.replaceInput.value,
                    isRegexMode: elements.regexMode.checked
                };
                this.updatePreview();
                this.updatePreviewArea();
            }

            updatePreview() {
                // 检查是否启用自动集数
                const autoEpisodeEnabled = document.getElementById('autoEpisodeCheck')?.checked || false;
                const startEpisode = parseInt(document.getElementById('startEpisode')?.value || '1');
                const episodeDigits = parseInt(document.getElementById('episodeDigits')?.value || '2');
                const episodePrefix = document.getElementById('episodePrefix')?.value || 'E';

                app.files.forEach((file, index) => {
                    let newName = this.generateNewName(file.originalName);

                    // 如果启用自动集数，添加集数信息
                    if (autoEpisodeEnabled) {
                        const episodeNumber = startEpisode + index;
                        const episodeStr = episodePrefix + episodeNumber.toString().padStart(episodeDigits, '0');

                        // 在文件名中添加集数（在扩展名前）
                        const lastDotIndex = newName.lastIndexOf('.');
                        if (lastDotIndex > 0) {
                            const nameWithoutExt = newName.substring(0, lastDotIndex);
                            const extension = newName.substring(lastDotIndex);
                            newName = nameWithoutExt + '.' + episodeStr + extension;
                        } else {
                            newName = newName + '.' + episodeStr;
                        }
                    }

                    file.newName = newName;

                    const previewElement = document.getElementById(`preview-${file.id}`);
                    if (previewElement) {
                        previewElement.textContent = newName !== file.originalName ? `→ ${newName}` : '';
                    }
                });

                // 检查重复命名
                this.checkDuplicateNames();
            }

            checkDuplicateNames() {
                const nameCount = {};
                const duplicates = new Set();

                // 统计新文件名出现次数
                app.files.forEach(file => {
                    const newName = file.newName || file.originalName;
                    nameCount[newName] = (nameCount[newName] || 0) + 1;
                    if (nameCount[newName] > 1) {
                        duplicates.add(newName);
                    }
                });

                // 为重复的文件名添加序号
                const duplicateCounters = {};
                app.files.forEach(file => {
                    const newName = file.newName || file.originalName;
                    if (duplicates.has(newName)) {
                        duplicateCounters[newName] = (duplicateCounters[newName] || 0) + 1;

                        // 添加序号
                        const lastDotIndex = newName.lastIndexOf('.');
                        if (lastDotIndex > 0) {
                            const nameWithoutExt = newName.substring(0, lastDotIndex);
                            const extension = newName.substring(lastDotIndex);
                            file.newName = `${nameWithoutExt}_${duplicateCounters[newName]}${extension}`;
                        } else {
                            file.newName = `${newName}_${duplicateCounters[newName]}`;
                        }

                        // 更新预览
                        const previewElement = document.getElementById(`preview-${file.id}`);
                        if (previewElement) {
                            previewElement.textContent = `→ ${file.newName}`;
                        }
                    }
                });
            }

            generateNewName(originalName) {
                let newName = originalName;
                const { prefix, suffix, findPattern, replaceWith, isRegexMode } = app.settings;

                // 步骤1: 先执行查找替换操作
                if (findPattern && replaceWith !== undefined) {
                    try {
                        if (isRegexMode) {
                            // 正则表达式模式
                            const regex = new RegExp(findPattern, 'g');
                            newName = newName.replace(regex, replaceWith);
                        } else {
                            // 普通文本替换模式
                            newName = newName.split(findPattern).join(replaceWith);
                        }
                    } catch (e) {
                        console.warn('Invalid pattern:', findPattern, e);
                    }
                }

                // 步骤2: 分离文件名和扩展名
                const lastDotIndex = newName.lastIndexOf('.');
                let nameWithoutExt = newName;
                let extension = '';

                if (lastDotIndex > 0) {
                    nameWithoutExt = newName.substring(0, lastDotIndex);
                    extension = newName.substring(lastDotIndex);
                }

                // 步骤3: 添加前缀（如果有）
                if (prefix) {
                    nameWithoutExt = prefix + nameWithoutExt;
                }

                // 步骤4: 添加后缀（如果有）
                if (suffix) {
                    nameWithoutExt = nameWithoutExt + suffix;
                }

                return nameWithoutExt + extension;
            }

            updatePreviewArea() {
                if (app.files.length === 0) {
                    elements.previewArea.innerHTML = `
                        <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                            添加文件后将显示重命名预览
                        </p>
                    `;
                    return;
                }

                const previewItems = app.files.slice(0, 5).map(file => {
                    const newName = this.generateNewName(file.originalName);
                    const hasChanges = newName !== file.originalName;

                    return `
                        <div class="preview-item">
                            <div class="preview-original">${file.originalName}</div>
                            ${hasChanges ? `
                                <div class="preview-arrow">→</div>
                                <div class="preview-new">${newName}</div>
                            ` : `
                                <div style="color: var(--text-secondary); font-style: italic;">无变化</div>
                            `}
                        </div>
                    `;
                }).join('');

                const moreFiles = app.files.length > 5 ? `
                    <div style="text-align: center; color: var(--text-secondary); font-size: 12px; margin-top: 8px;">
                        还有 ${app.files.length - 5} 个文件...
                    </div>
                ` : '';

                elements.previewArea.innerHTML = previewItems + moreFiles;
            }

            async executeRename() {
                if (app.files.length === 0) {
                    this.showNotification('请先添加文件', 'warning');
                    return;
                }

                const button = elements.renameBtn;
                const originalText = button.innerHTML;
                button.innerHTML = '<div class="loading"></div> 重命名中...';
                button.disabled = true;

                try {
                    // 模拟重命名过程（实际应用中这里会调用文件系统API）
                    await this.simulateRename();

                    // 添加到历史记录
                    app.addToHistory({
                        type: 'rename',
                        files: app.files.map(f => ({
                            id: f.id,
                            oldName: f.originalName,
                            newName: f.newName
                        })),
                        settings: { ...app.settings }
                    });

                    this.showNotification('重命名完成！', 'success');

                    // 更新原始名称为新名称
                    app.files.forEach(file => {
                        file.originalName = file.newName;
                    });

                    fileManager.renderFileList();
                    this.updatePreview();

                } catch (error) {
                    this.showNotification('重命名失败：' + error.message, 'error');
                } finally {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }

            async simulateRename() {
                // 模拟异步重命名过程
                return new Promise(resolve => {
                    setTimeout(resolve, 1000);
                });
            }

            showNotification(message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 16px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                    max-width: 300px;
                `;

                // 设置背景色
                const colors = {
                    success: '#10b981',
                    warning: '#f59e0b',
                    error: '#ef4444',
                    info: '#6366f1'
                };
                notification.style.background = colors[type];

                notification.textContent = message;
                document.body.appendChild(notification);

                // 自动移除
                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        }

        // 初始化管理器
        const fileManager = new FileManager();
        const renameEngine = new RenameEngine();

        // 主题切换
        elements.themeToggle.addEventListener('click', () => {
            app.toggleTheme();
        });

        // 历史操作
        elements.undoBtn.addEventListener('click', () => app.undo());
        elements.redoBtn.addEventListener('click', () => app.redo());

        // 快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'z':
                        e.preventDefault();
                        if (e.shiftKey) {
                            app.redo();
                        } else {
                            app.undo();
                        }
                        break;
                    case 'Enter':
                        e.preventDefault();
                        renameEngine.executeRename();
                        break;
                }
            }
        });

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            .notification {
                box-shadow: var(--shadow-lg);
            }
        `;
        document.head.appendChild(style);

        // 初始化历史按钮状态
        app.updateHistoryButtons();

        // 全局函数引用，供HTML onclick使用
        window.fileManager = fileManager;
        window.renameEngine = renameEngine;

        console.log('Renamify 应用初始化完成！');
    </script>
</body>
</html>
